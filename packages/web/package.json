{"name": "@onlyrules/web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "build:bun": "bun next build", "start": "next start", "lint": "next lint", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "postinstall": "prisma generate", "install:clean": "rm -rf node_modules package-lock.json && npm install", "type-check": "tsc --noEmit", "db:generate": "prisma generate", "db:push": "prisma db push", "db:studio": "prisma studio", "db:migrate": "prisma migrate dev", "db:migrate:deploy": "prisma migrate deploy", "db:migrate:reset": "prisma migrate reset", "db:seed": "prisma db seed", "lingui:extract": "lingui extract", "lingui:compile": "lingui compile"}, "dependencies": {"@codemirror/basic-setup": "^0.20.0", "@codemirror/lang-javascript": "^6.2.4", "@codemirror/lang-json": "^6.0.2", "@codemirror/lang-markdown": "^6.3.4", "@codemirror/lang-xml": "^6.1.0", "@codemirror/state": "^6.5.2", "@codemirror/theme-one-dark": "^6.1.3", "@codemirror/view": "^6.38.0", "@hookform/resolvers": "^3.9.0", "@lingui/cli": "^5.3.3", "@lingui/core": "^5.3.3", "@lingui/format-json": "^5.3.3", "@lingui/loader": "^5.3.3", "@lingui/macro": "^5.3.3", "@lingui/react": "^5.3.3", "@next/third-parties": "^15.3.5", "@onlyrules/docs": "file:../docs", "@onlyrules/shared": "file:../shared", "@prisma/client": "^6.11.1", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/themes": "^3.2.1", "@tailwindcss/typography": "^0.5.14", "@tanstack/react-query": "^5.84.1", "@tanstack/react-query-devtools": "^5.84.1", "@types/bcryptjs": "^3.0.0", "@types/react-dom": "18.2.7", "@uiw/react-codemirror": "^4.24.0", "autoprefixer": "10.4.15", "babel-plugin-macros": "^3.1.0", "bcryptjs": "^3.0.2", "better-auth": "^1.2.12", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "codemirror": "^6.0.2", "date-fns": "^3.6.0", "embla-carousel-react": "^8.3.0", "eslint": "8.49.0", "eslint-config-next": "15.3.5", "fumadocs-core": "^15.3.0", "fumadocs-mdx": "^11.7.1", "fumadocs-ui": "^15.3.0", "input-otp": "^1.2.4", "jose": "^6.0.11", "jotai": "^2.12.5", "lucide-react": "^0.446.0", "next": "^15.3.5", "next-intl": "^4.3.4", "next-themes": "^0.4.6", "onlyrules": "^0.0.24", "postcss": "8.4.30", "prisma": "^6.11.1", "react": "18.2.0", "react-day-picker": "^8.10.1", "react-dom": "18.2.0", "react-hook-form": "^7.53.0", "react-resizable-panels": "^2.1.3", "recharts": "^2.12.7", "sonner": "^1.5.0", "tailwind-merge": "^2.5.2", "tailwindcss": "^4.1.11", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.9", "zod": "^3.23.8"}, "devDependencies": {"@types/node": "24.1.0", "@types/react": "19.1.8", "@vitest/ui": "^3.2.4", "typescript": "^5.8.3", "vitest": "^3.2.4"}}